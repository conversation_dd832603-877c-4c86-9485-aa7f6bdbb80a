/* FreeCell Specific Styles */

.freecell-board {
    display: grid;
    grid-template-areas: 
        "top-area"
        "tableau";
    grid-template-rows: auto 1fr;
    gap: 20px;
    margin-bottom: 30px;
    position: relative;
    min-height: 100vh;
}

/* Top Area: Free Cells and Foundation Piles */
.top-area {
    grid-area: top-area;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
    padding: 10px 0;
}

.freecells-area {
    display: flex;
    gap: 15px;
    justify-content: flex-start;
}

.freecell-pile {
    width: 132px;
    height: 185px;
    border: 2px dashed rgba(255,255,255,0.3);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255,255,255,0.1);
    position: relative;
    transition: all 0.2s ease;
}



.foundation-area {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.foundation-pile {
    width: 132px;
    height: 185px;
    border: 2px dashed rgba(255,255,255,0.3);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255,255,255,0.1);
    position: relative;
    transition: all 0.2s ease;
}



/* Tableau Area */
.freecell-tableau {
    grid-area: tableau;
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
    padding-bottom: 50px;
}

.freecell-tableau .tableau-pile {
    width: auto;
    min-width: 100px;
    height: 200px;
    border: 2px dashed rgba(255,255,255,0.2);
    border-radius: 8px;
    background: rgba(255,255,255,0.05);
    position: relative;
    transition: all 0.2s ease;
}



/* Card Sequence Styles */
.card.sequence-valid {
    box-shadow: 0 0 0 2px #4CAF50, 0 4px 8px rgba(76, 175, 80, 0.4);
}

.card.sequence-invalid {
    box-shadow: 0 0 0 2px #f44336, 0 4px 8px rgba(244, 67, 54, 0.4);
}

.card.selected {
    box-shadow: 0 0 0 2px #ffeb3b, 0 4px 8px rgba(0,0,0,0.3);
    transform: translateY(-2px);
}

.card.multi-selected {
    box-shadow: 0 0 0 2px #4CAF50, 0 4px 8px rgba(0,0,0,0.3);
    transform: translateY(-1px);
}



/* Dragging Multi-card Sequences */
.card.dragging-multi {
    z-index: 9999 !important;
    pointer-events: none;
    position: fixed !important;
    box-shadow: 0 15px 30px rgba(0,0,0,0.7), 0 0 0 3px #ffeb3b;
    transition: none !important;
    filter: brightness(1.1);
}

.card.dragging-multi + .card.dragging-multi {
    z-index: 9998 !important;
    box-shadow: 0 12px 24px rgba(0,0,0,0.6), 0 0 0 2px #4CAF50;
    filter: brightness(1.05);
}

.card.dragging-multi + .card.dragging-multi + .card.dragging-multi {
    z-index: 9997 !important;
    box-shadow: 0 10px 20px rgba(0,0,0,0.5), 0 0 0 2px #2196F3;
    filter: brightness(1.02);
}

/* Auto-move animations */
.card.auto-moving {
    animation: autoMove 0.5s ease-in-out;
}

@keyframes autoMove {
    0% { transform: scale(1); }
    50% { transform: scale(1.1) rotate(5deg); }
    100% { transform: scale(1); }
}

/* Hint animations */
.card.hint-highlight {
    animation: hintPulse 1s ease-in-out infinite;
}

@keyframes hintPulse {
    0%, 100% {
        box-shadow: 0 0 0 2px transparent;
    }
    50% {
        box-shadow: 0 0 0 2px #ffeb3b, 0 0 10px rgba(255, 235, 59, 0.5);
    }
}

/* Mobile Responsive */
@media (max-width: 1024px) {
    .freecell-tableau {
        grid-template-columns: repeat(8, minmax(80px, 1fr));
        gap: 8px;
    }

    .freecell-tableau .tableau-pile {
        min-width: 80px;
        height: calc(100vh - 200px);
        max-width: 100px;
    }

    .freecells-area,
    .foundation-area {
        gap: 8px;
    }

    .freecell-pile,
    .foundation-pile {
        width: 80px;
        height: 110px;
    }
}

@media (max-width: 768px) {
    .top-area {
        flex-direction: column;
        gap: 15px;
        align-items: center;
    }

    .freecells-area,
    .foundation-area {
        justify-content: center;
        gap: 6px;
    }

    .freecell-pile,
    .foundation-pile {
        width: calc((100vw - 60px) / 4 - 6px);
        height: calc((100vw - 60px) / 4 * 1.4);
        max-width: 70px;
        max-height: 98px;
        min-width: 50px;
        min-height: 70px;
    }

    .freecell-tableau {
        grid-template-columns: repeat(8, 1fr);
        gap: 4px;
        padding: 0 4px;
    }

    .freecell-tableau .tableau-pile {
        min-width: calc((100vw - 40px) / 8 - 4px);
        width: calc((100vw - 40px) / 8 - 4px);
        height: calc(100vh - 280px);
        max-width: 60px;
        min-height: 150px;
    }
}

@media screen and (max-width: 768px) and (orientation: landscape) {
    .top-area {
        flex-direction: row;
        gap: 10px;
    }

    .freecells-area,
    .foundation-area {
        gap: 4px;
    }

    .freecell-pile,
    .foundation-pile {
        width: calc((100vw - 80px) / 8 - 4px);
        height: calc((100vw - 80px) / 8 * 1.4);
        max-width: 55px;
        max-height: 77px;
        min-width: 40px;
        min-height: 56px;
    }

    .freecell-tableau .tableau-pile {
        min-width: calc((100vw - 40px) / 8 - 2px);
        width: calc((100vw - 40px) / 8 - 2px);
        height: calc(100vh - 160px);
        max-width: 55px;
        min-height: 100px;
    }
}

/* Game Messages and Help Panel - inherit from base styles */
.hidden {
    display: none !important;
}

/* Additional FreeCell specific animations */
.card.hint-highlight {
    animation: freecellHint 2s ease-in-out infinite;
    border: 3px solid #ffeb3b !important;
    box-shadow: 0 0 20px rgba(255, 235, 59, 0.8) !important;
}

@keyframes freecellHint {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(255, 235, 59, 0.8);
    }
    50% {
        transform: scale(1.08);
        box-shadow: 0 0 30px rgba(255, 235, 59, 1);
    }
}



/* Card sequence highlighting */
.card.sequence-highlight {
    border: 2px solid #2196F3 !important;
    box-shadow: 0 0 10px rgba(33, 150, 243, 0.6) !important;
}

/* Foundation pile specific styling */
.foundation-pile[data-suit="hearts"] .pile-placeholder {
    color: #dc143c;
}

.foundation-pile[data-suit="diamonds"] .pile-placeholder {
    color: #dc143c;
}

.foundation-pile[data-suit="clubs"] .pile-placeholder {
    color: #000;
}

.foundation-pile[data-suit="spades"] .pile-placeholder {
    color: #000;
}

/* Enhanced mobile touch feedback */
@media (max-width: 768px) {
    .card.draggable-card:active {
        transition: transform 0.1s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.4);
    }

    .freecell-pile:active,
    .foundation-pile:active,
    .tableau-pile:active {
        background: rgba(255, 255, 255, 0.2) !important;
        transition: all 0.1s ease;
    }
}

/* Improved card stacking in tableau */
.freecell-tableau .tableau-pile .card {
    transition: all 0.3s ease;
}

.freecell-tableau .tableau-pile .card:hover {
    z-index: 100;
}

/* Card returning animation (like spider solitaire) */
.card.card-returning {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    z-index: 9998 !important;
}



/* Card flip animation for auto-complete */
.card.auto-flip {
    animation: cardFlip 0.6s ease-in-out;
}

@keyframes cardFlip {
    0% { transform: rotateY(0deg); }
    50% { transform: rotateY(90deg); }
    100% { transform: rotateY(0deg); }
}

/* Win celebration animation */
.card.celebration {
    animation: celebrate 2s ease-in-out;
}

@keyframes celebrate {
    0%, 100% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.1) rotate(5deg); }
    50% { transform: scale(1.2) rotate(-5deg); }
    75% { transform: scale(1.1) rotate(3deg); }
}
